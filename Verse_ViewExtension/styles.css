body {
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-image: url('background.jpg'); */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    font-family: 'Georgia', serif;
    opacity: 1;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-color: rgba(0, 0, 0, 0.5); */
}

.verse-container {
    max-width: 800px;
    padding: 2rem;
    text-align: center;
    color: white;
    background-color: rgba(25, 70, 130, 0.6); /* Blue tint with 60% opacity */
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(25, 70, 130, 0.5);
    display: none; /* Hide by default until loading is complete */
}

.verse-text {
    font-size: 2rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-style: italic;
}

.verse-reference {
    font-size: 1.2rem;
    font-weight: bold;
}

.verse-version {
    font-size: 0.9rem;
    font-weight: normal;
    margin-top: 0.5rem;
    opacity: 0.8;
    font-style: normal;
}

.date-picker {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    z-index: 1000;
}

.date-picker::-webkit-calendar-picker-indicator {
    filter: invert(1);
}

.support-link {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.support-link a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.support-link a:hover {
    color: #333;
    background-color: rgba(255, 255, 255, 0.9);
    text-decoration: underline;
}

.loading-message {
    max-width: 800px;
    padding: 2rem;
    text-align: center;
    color: white;
    background-color: rgba(25, 70, 130, 0.6); /* Blue tint with 60% opacity */
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(25, 70, 130, 0.5);
    position: absolute;
    z-index: 100;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.loading-message p {
    font-size: 1.5rem;
    font-style: italic;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #28a745;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    z-index: 1002;
    animation: slideDown 0.3s ease-out;
}

.notification.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
}

/* Settings Button */
.settings-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #333333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1001;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.settings-btn:hover {
    background-color: rgba(255, 255, 255, 0.95);
    transform: rotate(90deg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Settings Container */
.settings-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #4ba290 100%);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-content {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    color: #333;
}

.settings-content h2 {
    margin: 0 0 1.5rem 0;
    text-align: center;
    color: #333;
    font-size: 1.5rem;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: #667eea;
}

.settings-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.save-btn,
.cancel-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn {
    background-color: #667eea;
    color: white;
}

.save-btn:hover {
    background-color: #5a6fd8;
    transform: translateY(-2px);
}

.cancel-btn {
    background-color: #6c757d;
    color: white;
}

.cancel-btn:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}