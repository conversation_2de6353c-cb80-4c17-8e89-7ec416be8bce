steps:
  # Step 1: Build image from Dockerfile
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/verseview999/verse-view/verse-view:$SHORT_SHA'
      - '.'
    dir: 'Verse_ViewServer'

  # Step 2: Push image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/verseview999/verse-view/verse-view:$SHORT_SHA'

  # Step 3: Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'verse-view'
      - '--image'
      - 'us-central1-docker.pkg.dev/verseview999/verse-view/verse-view:$SHORT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--service-account'
      - '<EMAIL>'

images:
  - 'us-central1-docker.pkg.dev/verseview999/verse-view/verse-view:$SHORT_SHA'

options:
  logging: CLOUD_LOGGING_ONLY
