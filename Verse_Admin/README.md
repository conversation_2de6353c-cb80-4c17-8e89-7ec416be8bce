# Verse Admin

A simple tool to manage verse data in Firebase for the Verse View application.

## Setup

1. Create a Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. Set up Firestore Database in your Firebase project
3. Generate a private key for your Firebase project:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
4. Copy `.env.example` to `.env` and fill in the values from your Firebase private key JSON file
5. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

### Adding Verses

To add a single verse:

```
python add_verse.py --book "John" --chapter 3 --verse_start 16 --translation "NIV" --text "For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life." --image "cross-sunset.jpg"
```

To add a verse range:

```
python add_verse.py --book "Proverbs" --chapter 3 --verse_start 5 --verse_end 6 --translation "NIV" --text "Trust in the LORD with all your heart and lean not on your own understanding; in all your ways submit to him, and he will make your paths straight."
```

To add a translation to an existing verse:

```
python add_translation.py --verse_id "John_3_16" --translation "KJV" --text "For God so loved the world, that he gave his only begotten Son, that whosoever believeth in him should not perish, but have everlasting life."
```

To import verses from a CSV file:

```
python import_verses.py --file verses.csv
```

### Assigning Verses to Days

To assign a single verse to a specific day (using default year):

```
python assign_verse.py single --day 25 --month 12 --verse_id "John_3_16"
```

To assign a single verse to a specific day for a particular year:

```
python assign_verse.py single --day 25 --month 12 --verse_id "John_3_16" --year "2023"
```

To bulk assign verses from the verse_schedule.csv file (default year):

```
python assign_verse.py bulk
```

To bulk assign verses from a custom CSV file:

```
python assign_verse.py bulk --csv "my_schedule.csv" --year "2024"
```

To generate assignments for all days (using default year):

```
python generate_year.py
```

To generate assignments for a specific year:

```
python generate_year.py --year "2023"
```

### Populating Verses with KJV Text

To populate the verses collection with KJV text from the Bible API:

```
python populate_verses_from_api.py
```

To populate from a specific year mapping:

```
python populate_verses_from_api.py --year "2023"
```

To use a custom delay between API calls (default is 5 seconds):

```
python populate_verses_from_api.py --delay 3.0
```

### Web Admin Interface

To start the web-based admin interface:

```
./start_web_app.sh
```

Or run directly:

```
python web_app.py
```

The web interface will be available at: http://localhost:5000

Features:
- View all days of the year in a calendar table
- See verse references, text, and image names
- Edit verse text for different translations
- Add new translations to existing verses
- Update image names for verses
- Switch between different year mappings (default, 2023, 2024, etc.)

## Database Structure

The Firebase database uses the following structure:

### Days Collection
- Document ID: `DD-MM` format (e.g., "25-12" for December 25th)
- Structure:
  ```
  {
    "default": {
      "verseId": "John_3_16"
    },
    "2023": {
      "verseId": "Romans_8_28"
    }
  }
  ```

### Verses Collection
- Document ID: `BOOK_CHAPTER_VERSE` format (e.g., "John_3_16")
- Structure:
  ```
  {
    "book": "John",
    "chapter": 3,
    "verseStart": 16,
    "verseEnd": 16,
    "imageName": "cross-sunset.jpg"
  }
  ```
- Subcollection: `versions`
  - Document ID: Translation abbreviation in lowercase (e.g., "niv", "kjv")
  - Structure:
    ```
    {
      "text": "For God so loved the world...",
      "translation": "NIV"
    }
    ```

## File Formats

### CSV Import Format

The CSV file for importing verses should have the following columns:
- book: The book of the Bible (e.g., "John")
- chapter: The chapter number (e.g., 3)
- verse_start: The starting verse number (e.g., 16)
- verse_end: The ending verse number (optional, defaults to verse_start)
- translation: The translation abbreviation (e.g., "NIV", "KJV")
- text: The verse text
- image_name: The image filename (optional)

Example:
```
book,chapter,verse_start,verse_end,translation,text,image_name
John,3,16,16,NIV,"For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.",cross-sunset.jpg
John,3,16,16,KJV,"For God so loved the world, that he gave his only begotten Son, that whosoever believeth in him should not perish, but have everlasting life.",cross-sunset.jpg
Proverbs,3,5,6,NIV,"Trust in the LORD with all your heart and lean not on your own understanding; in all your ways submit to him, and he will make your paths straight.",winding-path.jpg
```

### Verse Schedule CSV Format

The verse_schedule.csv file for bulk assignment should have the following columns:
- day: The day in DD-MM format (e.g., "25-12" for December 25th)
- verse_reference: The verse ID (e.g., "John_3_16")

Example:
```
day,verse_reference
25-12,John_3_16
26-12,Luke_2_11
01-01,Romans_8_28
```
