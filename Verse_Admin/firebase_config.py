import os
import json
import firebase_admin
from firebase_admin import credentials, firestore
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def initialize_firebase():
    """Initialize Firebase Admin SDK if not already initialized"""
    try:
        # Check if already initialized
        firebase_admin.get_app()
    except ValueError:
        try:
            # First try to use environment variables
            if os.getenv("FIREBASE_PROJECT_ID"):
                # Create a credential dictionary from environment variables
                cred_dict = {
                    "type": "service_account",
                    "project_id": os.getenv("FIREBASE_PROJECT_ID"),
                    "private_key_id": os.getenv("FIREBASE_PRIVATE_KEY_ID"),
                    "private_key": os.getenv("FIREBASE_PRIVATE_KEY").replace('\\n', '\n'),
                    "client_email": os.getenv("FIREBASE_CLIENT_EMAIL"),
                    "client_id": os.getenv("FIREBASE_CLIENT_ID"),
                    "auth_uri": os.getenv("FIREBASE_AUTH_URI"),
                    "token_uri": os.getenv("FIREBASE_TOKEN_URI"),
                    "auth_provider_x509_cert_url": os.getenv("FIREBASE_AUTH_PROVIDER_X509_CERT_URL"),
                    "client_x509_cert_url": os.getenv("FIREBASE_CLIENT_X509_CERT_URL")
                }
                cred = credentials.Certificate(cred_dict)
                firebase_admin.initialize_app(cred)
                print("Firebase Admin SDK initialized successfully using environment variables")

            # If environment variables not set, look for a JSON file
            else:
                # Look for the credentials file in common locations
                possible_paths = [
                    "firebase-credentials.json",
                    "serviceAccountKey.json",
                    os.path.expanduser("~/firebase-credentials.json")
                ]

                # Use the first file that exists
                cred_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        cred_path = path
                        break

                if cred_path:
                    cred = credentials.Certificate(cred_path)
                    firebase_admin.initialize_app(cred)
                    print(f"Firebase Admin SDK initialized successfully using {cred_path}")
                else:
                    print("Error: No Firebase credentials found. Please set up environment variables or provide a credentials JSON file.")
                    print("You can place the JSON file in one of these locations:")
                    for path in possible_paths:
                        print(f"- {path}")
                    raise FileNotFoundError("Firebase credentials not found")

        except Exception as e:
            print(f"Error initializing Firebase Admin SDK: {e}")
            raise

def get_db():
    """Get Firestore database instance"""
    initialize_firebase()
    return firestore.client()
