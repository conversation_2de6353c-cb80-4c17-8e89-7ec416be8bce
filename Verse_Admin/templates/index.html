{% extends "base.html" %}

{% block title %}Verse Calendar - Verse Admin{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-6">
        <h1>Verse Calendar</h1>
        <p class="text-muted">Year: {{ current_year }}</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="?year=default&month={{ navigation.current_month }}" class="btn btn-outline-primary {% if current_year == 'default' %}active{% endif %}">Default</a>
            <a href="?year=2023&month={{ navigation.current_month }}" class="btn btn-outline-primary {% if current_year == '2023' %}active{% endif %}">2023</a>
            <a href="?year=2024&month={{ navigation.current_month }}" class="btn btn-outline-primary {% if current_year == '2024' %}active{% endif %}">2024</a>
        </div>
    </div>
</div>

<!-- Save Button and Month Navigation -->
<div class="row mb-3">
    <div class="col-12 text-center">
        <button id="saveAllBtn" class="btn btn-success btn-lg me-3" onclick="saveAllChanges()" disabled>
            <i class="bi bi-floppy"></i> Save All Changes
        </button>
        <span id="changeIndicator" class="text-muted" style="display: none;">
            <i class="bi bi-exclamation-circle"></i> You have unsaved changes
        </span>
    </div>
</div>

<!-- Month Navigation -->
<div class="month-navigation">
    <div class="d-flex justify-content-between align-items-center">
        <a href="?year={{ current_year }}&month={{ navigation.prev_month }}" class="btn btn-outline-secondary month-nav-btn">
            <i class="bi bi-chevron-left"></i> {{ navigation.prev_month_name }}
        </a>
        <div class="text-center">
            <h2 class="text-primary mb-0 current-month-title">{{ navigation.current_month_name }}</h2>
            <small class="text-muted">Month {{ navigation.current_month }} of 12</small>
        </div>
        <a href="?year={{ current_year }}&month={{ navigation.next_month }}" class="btn btn-outline-secondary month-nav-btn">
            {{ navigation.next_month_name }} <i class="bi bi-chevron-right"></i>
        </a>
    </div>
</div>

<!-- Single Month Display -->
<div class="month-section">
    <div class="table-responsive">
        <table class="table table-bordered table-sm">
            <thead>
                <tr class="day-header">
                    <th style="width: 50px;">Day</th>
                    <th style="width: 150px;">Reference</th>
                    <th>Text (First Version)</th>
                    <th style="width: 120px;">Image</th>
                    <th style="width: 80px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for day_num, day_data in month_data.days.items() %}
                <tr>
                    <td class="day-header">{{ day_num }}</td>
                    {% if day_data %}
                    <td class="verse-reference">
                        {% if day_data %}
                            {{ day_data.book }} {{ day_data.chapter }}:{{ day_data.verseStart }}{% if day_data.verseEnd != day_data.verseStart %}-{{ day_data.verseEnd }}{% endif %}
                        {% else %}
                            <input type="text"
                                   class="form-control form-control-sm editable-reference"
                                   placeholder="Book Chapter:Verse"
                                   data-day="{{ day_num }}"
                                   data-month="{{ navigation.current_month }}"
                                   data-field="reference"
                                   onblur="trackChange(this)"
                                   style="font-size: 0.9em;">
                        {% endif %}
                    </td>
                    <td class="verse-cell">
                        {% if day_data.versions %}
                            {% for version_id, version_data in day_data.versions.items() %}
                            <div class="version-container mb-2">
                                <strong>{{ version_data.translation }}:</strong>
                                <div class="editable-text"
                                     contenteditable="true"
                                     data-day="{{ day_num }}"
                                     data-month="{{ navigation.current_month }}"
                                     data-verse-id="{{ day_data.verse_id }}"
                                     data-version="{{ version_id }}"
                                     data-field="text"
                                     onblur="trackChange(this)"
                                     style="border: 1px solid #ddd; padding: 5px; border-radius: 3px; min-height: 40px;">{{ version_data.text }}</div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="version-container">
                                <strong>NIV:</strong>
                                <div class="editable-text"
                                     contenteditable="true"
                                     data-day="{{ day_num }}"
                                     data-month="{{ navigation.current_month }}"
                                     data-verse-id="new"
                                     data-version="niv"
                                     data-field="text"
                                     onblur="trackChange(this)"
                                     style="border: 1px solid #ddd; padding: 5px; border-radius: 3px; min-height: 40px;"
                                     placeholder="Enter verse text..."></div>
                            </div>
                        {% endif %}
                    </td>
                    <td class="image-name">
                        <input type="text"
                               class="form-control editable-image"
                               value="{{ day_data.imageName or '' }}"
                               data-day="{{ day_num }}"
                               data-month="{{ navigation.current_month }}"
                               data-verse-id="{{ day_data.verse_id or 'new' }}"
                               data-field="imageName"
                               onblur="trackChange(this)"
                               placeholder="image.jpg">
                    </td>
                    <td>
                        <a href="{{ url_for('verse_detail', verse_id=day_data.verse_id) }}" class="btn btn-sm btn-outline-info edit-btn">View</a>
                        <a href="{{ url_for('edit_verse', verse_id=day_data.verse_id) }}" class="btn btn-sm btn-outline-warning edit-btn">Edit</a>
                    </td>
                    {% else %}
                    <td colspan="4" class="text-muted text-center">No verse assigned</td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Track changes for bulk saving
let changes = {};
let hasUnsavedChanges = false;

function trackChange(element) {
    const day = element.getAttribute('data-day');
    const month = element.getAttribute('data-month');
    const verseId = element.getAttribute('data-verse-id');
    const version = element.getAttribute('data-version');
    const field = element.getAttribute('data-field');

    const dayKey = `${day.padStart(2, '0')}-${month.padStart(2, '0')}`;

    // Initialize change tracking for this day
    if (!changes[dayKey]) {
        changes[dayKey] = {
            day: parseInt(day),
            month: parseInt(month),
            verseId: verseId,
            versions: {},
            imageName: null
        };
    }

    if (field === 'text') {
        // Track text changes
        if (!changes[dayKey].versions[version]) {
            changes[dayKey].versions[version] = {};
        }
        changes[dayKey].versions[version].text = element.textContent || element.innerText;
        changes[dayKey].versions[version].translation = version.toUpperCase();
    } else if (field === 'imageName') {
        // Track image name changes
        changes[dayKey].imageName = element.value;
    } else if (field === 'reference') {
        // Track reference changes for new verses
        changes[dayKey].reference = element.value;
    }

    // Update UI to show unsaved changes
    hasUnsavedChanges = Object.keys(changes).length > 0;
    updateSaveButton();

    // Add visual indicator to changed field
    element.style.backgroundColor = '#fff3cd';
    element.style.borderColor = '#ffc107';
}

function updateSaveButton() {
    const saveBtn = document.getElementById('saveAllBtn');
    const indicator = document.getElementById('changeIndicator');

    if (hasUnsavedChanges) {
        saveBtn.disabled = false;
        indicator.style.display = 'inline';
    } else {
        saveBtn.disabled = true;
        indicator.style.display = 'none';
    }
}

function saveAllChanges() {
    if (!hasUnsavedChanges) return;

    const saveBtn = document.getElementById('saveAllBtn');
    const originalText = saveBtn.innerHTML;

    // Show loading state
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Saving...';
    saveBtn.disabled = true;

    // Send changes to backend
    fetch('/api/bulk_update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            year: '{{ current_year }}',
            changes: changes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Clear changes and update UI
            changes = {};
            hasUnsavedChanges = false;
            updateSaveButton();

            // Remove visual indicators from changed fields
            document.querySelectorAll('.editable-text, .editable-image').forEach(el => {
                el.style.backgroundColor = '';
                el.style.borderColor = '';
            });

            // Show success message
            showMessage('All changes saved successfully!', 'success');

            // Reload page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showMessage('Error saving changes: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error saving changes: ' + error.message, 'error');
    })
    .finally(() => {
        // Restore button state
        saveBtn.innerHTML = originalText;
        updateSaveButton();
    });
}

function showMessage(message, type) {
    // Create and show a temporary message
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the content
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Add keyboard navigation for month switching (only when not editing)
document.addEventListener('keydown', function(event) {
    // Only handle arrow keys when not in an editable field
    if (event.target.contentEditable === 'true' ||
        event.target.tagName.toLowerCase() === 'input' ||
        event.target.tagName.toLowerCase() === 'textarea') {
        return;
    }

    if (event.key === 'ArrowLeft') {
        // Go to previous month
        event.preventDefault();
        if (hasUnsavedChanges) {
            if (confirm('You have unsaved changes. Are you sure you want to leave this page?')) {
                window.location.href = '?year={{ current_year }}&month={{ navigation.prev_month }}';
            }
        } else {
            window.location.href = '?year={{ current_year }}&month={{ navigation.prev_month }}';
        }
    } else if (event.key === 'ArrowRight') {
        // Go to next month
        event.preventDefault();
        if (hasUnsavedChanges) {
            if (confirm('You have unsaved changes. Are you sure you want to leave this page?')) {
                window.location.href = '?year={{ current_year }}&month={{ navigation.next_month }}';
            }
        } else {
            window.location.href = '?year={{ current_year }}&month={{ navigation.next_month }}';
        }
    }
});

// Add loading indicator for navigation buttons
document.querySelectorAll('.month-nav-btn').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
        if (hasUnsavedChanges) {
            if (!confirm('You have unsaved changes. Are you sure you want to leave this page?')) {
                e.preventDefault();
                return;
            }
        }
        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Loading...';
    });
});

// Warn before leaving page with unsaved changes
window.addEventListener('beforeunload', function(e) {
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>
{% endblock %}
