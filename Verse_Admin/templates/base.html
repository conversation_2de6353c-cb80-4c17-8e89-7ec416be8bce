<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Verse Admin{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .verse-cell {
            font-size: 0.8em;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .day-header {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        .verse-reference {
            font-weight: bold;
            color: #0066cc;
        }
        .verse-text {
            font-style: italic;
            color: #666;
        }
        .image-name {
            font-size: 0.7em;
            color: #999;
        }
        .month-section {
            margin-bottom: 2rem;
        }
        .edit-btn {
            font-size: 0.7em;
            padding: 2px 6px;
        }
        .table-responsive {
            max-height: 80vh;
            overflow-y: auto;
        }
        .month-navigation {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .month-nav-btn {
            min-width: 120px;
        }
        .current-month-title {
            font-size: 1.8rem;
            font-weight: 600;
        }
        .month-nav-btn:hover {
            transform: translateY(-1px);
            transition: transform 0.2s ease;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .editable-text {
            min-height: 40px;
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 4px;
            background-color: #fff;
            transition: all 0.2s ease;
        }
        .editable-text:hover {
            border-color: #007bff;
            box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
        }
        .editable-text:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .editable-text[contenteditable="true"]:empty:before {
            content: attr(placeholder);
            color: #6c757d;
            font-style: italic;
        }
        .editable-image {
            transition: all 0.2s ease;
        }
        .editable-image:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .version-container {
            margin-bottom: 10px;
        }
        .version-container:last-child {
            margin-bottom: 0;
        }
        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .editable-reference {
            font-weight: bold;
            color: #0066cc;
        }
        .table td {
            position: relative;
        }
        .editable-text:hover::after,
        .editable-image:hover::after,
        .editable-reference:hover::after {
            content: "✏️";
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 12px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">Verse Admin</a>
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('index') }}">Calendar View</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
