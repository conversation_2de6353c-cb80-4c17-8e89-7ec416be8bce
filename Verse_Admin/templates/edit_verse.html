{% extends "base.html" %}

{% block title %}Edit {{ verse.book }} {{ verse.chapter }}:{{ verse.verseStart }} - Verse Admin{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Calendar</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('verse_detail', verse_id=verse.verse_id) }}">{{ verse.book }} {{ verse.chapter }}:{{ verse.verseStart }}</a></li>
                <li class="breadcrumb-item active">Edit</li>
            </ol>
        </nav>

        <h1>Edit {{ verse.book }} {{ verse.chapter }}:{{ verse.verseStart }}{% if verse.verseEnd != verse.verseStart %}-{{ verse.verseEnd }}{% endif %}</h1>
        
        <form id="editForm">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Book</label>
                                <input type="text" class="form-control" value="{{ verse.book }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Chapter</label>
                                <input type="text" class="form-control" value="{{ verse.chapter }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Verse</label>
                                <input type="text" class="form-control" value="{{ verse.verseStart }}{% if verse.verseEnd != verse.verseStart %}-{{ verse.verseEnd }}{% endif %}" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="imageName" class="form-label">Image Name</label>
                        <input type="text" class="form-control" id="imageName" name="imageName" value="{{ verse.imageName or '' }}" placeholder="e.g., cross-sunset.jpg">
                        <div class="form-text">Enter the filename of the image associated with this verse.</div>
                    </div>
                </div>
            </div>

            <!-- Translations -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Translations</h5>
                    <button type="button" class="btn btn-sm btn-success" onclick="addNewVersion()">Add Translation</button>
                </div>
                <div class="card-body" id="versionsContainer">
                    {% if verse.versions %}
                        {% for version_id, version_data in verse.versions.items() %}
                        <div class="version-group mb-4 p-3 border rounded" data-version="{{ version_id }}">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Translation</label>
                                    <input type="text" class="form-control version-translation" value="{{ version_data.translation }}" readonly>
                                </div>
                                <div class="col-md-9">
                                    <label class="form-label">Text</label>
                                    <textarea class="form-control version-text" rows="3">{{ version_data.text }}</textarea>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No translations available. Add one using the button above.</p>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                    <a href="{{ url_for('verse_detail', verse_id=verse.verse_id) }}" class="btn btn-secondary">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Add Version Modal -->
<div class="modal fade" id="addVersionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Translation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addVersionForm">
                    <div class="mb-3">
                        <label for="newVersionId" class="form-label">Translation ID</label>
                        <input type="text" class="form-control" id="newVersionId" placeholder="e.g., esv, nasb, nlt" required>
                        <div class="form-text">Use lowercase abbreviation (e.g., kjv, niv, esv)</div>
                    </div>
                    <div class="mb-3">
                        <label for="newVersionTranslation" class="form-label">Translation Name</label>
                        <input type="text" class="form-control" id="newVersionTranslation" placeholder="e.g., ESV, NASB, NLT" required>
                    </div>
                    <div class="mb-3">
                        <label for="newVersionText" class="form-label">Text</label>
                        <textarea class="form-control" id="newVersionText" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveNewVersion()">Add Translation</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addNewVersion() {
    const modal = new bootstrap.Modal(document.getElementById('addVersionModal'));
    modal.show();
}

function saveNewVersion() {
    const versionId = document.getElementById('newVersionId').value.toLowerCase().trim();
    const translation = document.getElementById('newVersionTranslation').value.trim();
    const text = document.getElementById('newVersionText').value.trim();

    if (!versionId || !translation || !text) {
        alert('Please fill in all fields');
        return;
    }

    // Add to the form
    const container = document.getElementById('versionsContainer');
    const versionHtml = `
        <div class="version-group mb-4 p-3 border rounded" data-version="${versionId}">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Translation</label>
                    <input type="text" class="form-control version-translation" value="${translation}" readonly>
                </div>
                <div class="col-md-9">
                    <label class="form-label">Text</label>
                    <textarea class="form-control version-text" rows="3">${text}</textarea>
                </div>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', versionHtml);

    // Clear form and close modal
    document.getElementById('addVersionForm').reset();
    bootstrap.Modal.getInstance(document.getElementById('addVersionModal')).hide();
}

document.getElementById('editForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Collect form data
    const imageName = document.getElementById('imageName').value.trim();
    const versions = {};

    document.querySelectorAll('.version-group').forEach(group => {
        const versionId = group.dataset.version;
        const translation = group.querySelector('.version-translation').value;
        const text = group.querySelector('.version-text').value;

        versions[versionId] = {
            translation: translation,
            text: text
        };
    });

    const data = {
        imageName: imageName,
        versions: versions
    };

    // Send update request
    fetch(`/api/update_verse/{{ verse.verse_id }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Verse updated successfully!');
            window.location.href = "{{ url_for('verse_detail', verse_id=verse.verse_id) }}";
        } else {
            alert('Error updating verse: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating verse');
    });
});
</script>
{% endblock %}
