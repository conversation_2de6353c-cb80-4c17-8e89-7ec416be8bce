{% extends "base.html" %}

{% block title %}{{ verse.book }} {{ verse.chapter }}:{{ verse.verseStart }} - Verse Admin{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Calendar</a></li>
                <li class="breadcrumb-item active">{{ verse.book }} {{ verse.chapter }}:{{ verse.verseStart }}</li>
            </ol>
        </nav>

        <h1>{{ verse.book }} {{ verse.chapter }}:{{ verse.verseStart }}{% if verse.verseEnd != verse.verseStart %}-{{ verse.verseEnd }}{% endif %}</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Verse Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Book:</strong> {{ verse.book }}</p>
                        <p><strong>Chapter:</strong> {{ verse.chapter }}</p>
                        <p><strong>Verse:</strong> {{ verse.verseStart }}{% if verse.verseEnd != verse.verseStart %}-{{ verse.verseEnd }}{% endif %}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Image:</strong> {{ verse.imageName or 'No image assigned' }}</p>
                        <p><strong>Verse ID:</strong> <code>{{ verse.verse_id }}</code></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Translations</h5>
            </div>
            <div class="card-body">
                {% if verse.versions %}
                    {% for version_id, version_data in verse.versions.items() %}
                    <div class="mb-4 p-3 border rounded">
                        <h6 class="text-primary">{{ version_data.translation }} ({{ version_id }})</h6>
                        <p class="mb-0">{{ version_data.text }}</p>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No translations available for this verse.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Actions</h6>
            </div>
            <div class="card-body">
                <a href="{{ url_for('edit_verse', verse_id=verse.verse_id) }}" class="btn btn-warning btn-block mb-2 w-100">Edit Verse</a>
                <a href="{{ url_for('index') }}" class="btn btn-secondary btn-block w-100">Back to Calendar</a>
            </div>
        </div>
        
        {% if verse.imageName %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Associated Image</h6>
            </div>
            <div class="card-body">
                <p class="mb-0"><strong>Filename:</strong> {{ verse.imageName }}</p>
                <small class="text-muted">Image preview not available in admin panel</small>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
