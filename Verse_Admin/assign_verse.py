#!/usr/bin/env python3
import argparse
import csv
from firebase_config import get_db

def assign_verse(day, month, verse_id, year="default"):
    """
    Assign a verse to a specific day

    Args:
        day (int): Day of the month (1-31)
        month (int): Month (1-12)
        verse_id (str): Verse ID (e.g., "John_3_16")
        year (str): Year identifier (default: "default")

    Returns:
        bool: True if successful
    """
    # Format the day ID (DD-MM)
    day_id = f"{day:02d}-{month:02d}"

    # Get Firestore database
    db = get_db()

    # Check if the verse exists
    verse_ref = db.collection('verses').document(verse_id)
    verse_doc = verse_ref.get()

    if not verse_doc.exists:
        print(f"Error: Verse {verse_id} does not exist")
        return False

    # Create the day document with year mapping structure
    day_data = {
        year: {
            "verseId": verse_id
        }
    }

    # Add the day to the days collection (merge to preserve other years if they exist)
    day_ref = db.collection('days').document(day_id)
    day_ref.set(day_data, merge=True)

    print(f"Assigned verse {verse_id} to day {day_id} for year {year}")
    return True

def bulk_assign_from_csv(csv_file_path, year="default"):
    """
    Bulk assign verses from a CSV file to the days collection

    Args:
        csv_file_path (str): Path to the CSV file
        year (str): Year identifier (default: "default")

    Returns:
        int: Number of assignments made
    """
    # Get Firestore database
    db = get_db()

    count = 0
    batch = db.batch()
    batch_size = 0
    max_batch_size = 500  # Firestore batch limit

    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                try:
                    # Extract data from the row
                    day_id = row['day'].strip()  # Should be in DD-MM format
                    verse_id = row['verse_reference'].strip()

                    # Validate day_id format (should be DD-MM)
                    if len(day_id) != 5 or day_id[2] != '-':
                        print(f"Warning: Invalid day format '{day_id}', skipping")
                        continue

                    # Create the day document with year mapping structure
                    day_data = {
                        year: {
                            "verseId": verse_id
                        }
                    }

                    # Add to batch
                    day_ref = db.collection('days').document(day_id)
                    batch.set(day_ref, day_data, merge=True)

                    batch_size += 1
                    count += 1

                    # Commit the batch if it's getting too large
                    if batch_size >= max_batch_size:
                        batch.commit()
                        print(f"Committed batch of {batch_size} assignments")
                        batch = db.batch()
                        batch_size = 0

                except (KeyError, ValueError) as e:
                    print(f"Error processing row: {row}")
                    print(f"Error details: {e}")

        # Commit any remaining assignments
        if batch_size > 0:
            batch.commit()
            print(f"Committed final batch of {batch_size} assignments")

        print(f"Successfully assigned {count} verses from {csv_file_path} to year '{year}'")
        return count

    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return 0

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Assign verses to specific days')

    # Create subparsers for different modes
    subparsers = parser.add_subparsers(dest='mode', help='Assignment mode')

    # Single verse assignment
    single_parser = subparsers.add_parser('single', help='Assign a single verse to a specific day')
    single_parser.add_argument('--day', required=True, type=int, help='Day of the month (1-31)')
    single_parser.add_argument('--month', required=True, type=int, help='Month (1-12)')
    single_parser.add_argument('--verse_id', required=True, help='Verse ID (e.g., "John_3_16")')
    single_parser.add_argument('--year', default="default", help='Year identifier (default: "default")')

    # Bulk CSV assignment
    bulk_parser = subparsers.add_parser('bulk', help='Bulk assign verses from CSV file')
    bulk_parser.add_argument('--csv', default="verse_schedule.csv", help='Path to CSV file (default: verse_schedule.csv)')
    bulk_parser.add_argument('--year', default="default", help='Year identifier (default: "default")')

    args = parser.parse_args()

    if args.mode == 'single':
        # Validate inputs
        if args.day < 1 or args.day > 31:
            print("Error: Day must be between 1 and 31")
            exit(1)

        if args.month < 1 or args.month > 12:
            print("Error: Month must be between 1 and 12")
            exit(1)

        # Assign the single verse
        assign_verse(args.day, args.month, args.verse_id, args.year)

    elif args.mode == 'bulk':
        # Bulk assign from CSV
        bulk_assign_from_csv(args.csv, args.year)

    else:
        # Default to bulk mode if no mode specified
        print("No mode specified. Using bulk mode with default CSV file.")
        bulk_assign_from_csv("verse_schedule.csv", "default")
