#!/usr/bin/env python3
import sys
import os
import argparse
from firebase_config import get_db

def migrate_verse_data(verse_data_path):
    """
    Migrate existing verse data from the static file to Firebase

    Args:
        verse_data_path (str): Path to the verse_data.py file

    Returns:
        int: Number of verses migrated
    """
    # Add the parent directory to the Python path
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(verse_data_path)))
    sys.path.append(parent_dir)

    try:
        # Import the VERSE_DATA dictionary
        module_name = os.path.basename(verse_data_path).replace('.py', '')
        module_path = os.path.dirname(verse_data_path)
        sys.path.append(module_path)

        # Import the module
        verse_data_module = __import__(module_name)
        VERSE_DATA = getattr(verse_data_module, 'VERSE_DATA')

        # Get Firestore database
        db = get_db()

        # Migrate the data
        count = 0
        batch = db.batch()
        batch_size = 0
        max_batch_size = 500  # Firestore batch limit

        for day_id, verse_data in VERSE_DATA.items():
            # Extract book, chapter, verse from the reference
            reference = verse_data['reference']
            text = verse_data['text']

            # Parse the reference to get book, chapter, verse
            parts = reference.split()

            # Handle different reference formats
            try:
                if len(parts) == 2:
                    # Format: "Book Chapter:Verse"
                    book = parts[0]
                    chapter_verse = parts[1].split(':')
                    chapter = int(chapter_verse[0])
                    verse = int(chapter_verse[1])
                elif len(parts) == 3:
                    # Format: "1 Corinthians 13:4"
                    book = f"{parts[0]} {parts[1]}"
                    chapter_verse = parts[2].split(':')
                    chapter = int(chapter_verse[0])
                    verse = int(chapter_verse[1])
                else:
                    print(f"Warning: Could not parse reference: {reference}")
                    continue

                # Create a verse ID
                verse_id = f"{book}_{chapter}_{verse}"

                # Create a document in the days collection with new structure
                day_data = {
                    "default": {
                        "verseId": verse_id
                    }
                }
                day_ref = db.collection('days').document(day_id)
                batch.set(day_ref, day_data)

                # Create a document in the verses collection with new structure
                verse_ref = db.collection('verses').document(verse_id)
                verse_doc = {
                    "book": book,
                    "chapter": chapter,
                    "verseStart": verse,
                    "verseEnd": verse
                    # imageName will be added separately if needed
                }
                batch.set(verse_ref, verse_doc)

                # Create a version document in the versions subcollection (assuming NIV as default)
                version_ref = verse_ref.collection('versions').document('niv')
                version_doc = {
                    "text": text,
                    "translation": "NIV"
                }
                batch.set(version_ref, version_doc)

                batch_size += 3  # We added 3 documents (day, verse, version)
                count += 1

            except (ValueError, IndexError) as e:
                print(f"Warning: Could not parse reference: {reference} - {e}")
                continue

            # Commit the batch if it's getting too large
            if batch_size >= max_batch_size - 3:
                batch.commit()
                print(f"Committed batch of {batch_size} documents")
                batch = db.batch()
                batch_size = 0

        # Commit any remaining documents
        if batch_size > 0:
            batch.commit()
            print(f"Committed final batch of {batch_size} documents")

        print(f"Migrated {count} verses")
        return count

    except Exception as e:
        print(f"Error migrating verse data: {e}")
        return 0

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Migrate existing verse data to Firebase')
    parser.add_argument('--file', required=True, help='Path to the verse_data.py file')

    args = parser.parse_args()

    # Migrate the data
    migrate_verse_data(args.file)
