#!/usr/bin/env python3
import argparse
from firebase_config import get_db

def add_translation(verse_id, translation, text):
    """
    Add a translation to an existing verse
    
    Args:
        verse_id (str): Verse ID (e.g., "John_3_16")
        translation (str): Translation abbreviation (e.g., "NIV", "KJV")
        text (str): Verse text
    
    Returns:
        bool: True if successful
    """
    # Get Firestore database
    db = get_db()
    
    # Check if the verse exists
    verse_ref = db.collection('verses').document(verse_id)
    verse_doc = verse_ref.get()
    
    if not verse_doc.exists:
        print(f"Error: Verse {verse_id} does not exist")
        return False
    
    # Add the translation to the versions subcollection
    translation_data = {
        "text": text,
        "translation": translation.upper()
    }
    
    version_ref = verse_ref.collection('versions').document(translation.lower())
    version_ref.set(translation_data)
    
    print(f"Added {translation} translation to verse: {verse_id}")
    return True

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Add a translation to an existing verse')
    parser.add_argument('--verse_id', required=True, help='Verse ID (e.g., "John_3_16")')
    parser.add_argument('--translation', required=True, help='Translation abbreviation (e.g., "NIV", "KJV")')
    parser.add_argument('--text', required=True, help='Verse text')
    
    args = parser.parse_args()
    
    # Add the translation
    add_translation(args.verse_id, args.translation, args.text)
