#!/usr/bin/env python3
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_cors import CORS
import calendar
from datetime import datetime
from firebase_config import get_db

app = Flask(__name__)
CORS(app)

# Initialize Firebase
db = get_db()

def get_month_days_data(year="default", month=None):
    """Get days data for a specific month with verse information"""
    days_ref = db.collection('days')
    days = days_ref.stream()

    days_data = {}

    for day_doc in days:
        day_id = day_doc.id  # DD-MM format
        day_data = day_doc.to_dict()

        # If month is specified, only include days from that month
        if month is not None:
            day_month = int(day_id.split('-')[1])
            if day_month != month:
                continue

        if year in day_data:
            verse_id = day_data[year].get('verseId')
            if verse_id:
                # Get verse details
                verse_ref = db.collection('verses').document(verse_id)
                verse_doc = verse_ref.get()

                if verse_doc.exists:
                    verse_data = verse_doc.to_dict()

                    # Get all versions
                    versions_ref = verse_ref.collection('versions')
                    versions = versions_ref.stream()

                    verse_versions = {}
                    for version_doc in versions:
                        version_data = version_doc.to_dict()
                        verse_versions[version_doc.id] = {
                            'text': version_data.get('text', ''),
                            'translation': version_data.get('translation', '')
                        }

                    days_data[day_id] = {
                        'verse_id': verse_id,
                        'book': verse_data.get('book', ''),
                        'chapter': verse_data.get('chapter', ''),
                        'verseStart': verse_data.get('verseStart', ''),
                        'verseEnd': verse_data.get('verseEnd', ''),
                        'imageName': verse_data.get('imageName', ''),
                        'versions': verse_versions
                    }

    return days_data

def get_all_days_data(year="default"):
    """Get all days data with verse information (for backward compatibility)"""
    return get_month_days_data(year, None)

def get_verse_details(verse_id):
    """Get detailed information about a specific verse"""
    verse_ref = db.collection('verses').document(verse_id)
    verse_doc = verse_ref.get()
    
    if not verse_doc.exists:
        return None
    
    verse_data = verse_doc.to_dict()
    
    # Get all versions
    versions_ref = verse_ref.collection('versions')
    versions = versions_ref.stream()
    
    verse_versions = {}
    for version_doc in versions:
        version_data = version_doc.to_dict()
        verse_versions[version_doc.id] = {
            'text': version_data.get('text', ''),
            'translation': version_data.get('translation', '')
        }
    
    return {
        'verse_id': verse_id,
        'book': verse_data.get('book', ''),
        'chapter': verse_data.get('chapter', ''),
        'verseStart': verse_data.get('verseStart', ''),
        'verseEnd': verse_data.get('verseEnd', ''),
        'imageName': verse_data.get('imageName', ''),
        'versions': verse_versions
    }

def get_days_in_month(month):
    """Get number of days in a given month"""
    if month in [1, 3, 5, 7, 8, 10, 12]:
        return 31
    elif month in [4, 6, 9, 11]:
        return 30
    else:
        return 29  # February (including leap day)

def get_prev_next_month(current_month):
    """Get previous and next month numbers"""
    prev_month = current_month - 1 if current_month > 1 else 12
    next_month = current_month + 1 if current_month < 12 else 1
    return prev_month, next_month

@app.route('/')
def index():
    """Main page showing one month at a time"""
    year = request.args.get('year', 'default')
    month = int(request.args.get('month', datetime.now().month))

    # Ensure month is valid
    if month < 1 or month > 12:
        month = datetime.now().month

    # Get data for the specific month only
    days_data = get_month_days_data(year, month)

    # Create calendar structure for the single month
    month_name = calendar.month_name[month]
    days_in_month = get_days_in_month(month)

    month_data = {
        'name': month_name,
        'number': month,
        'days': {}
    }

    for day in range(1, days_in_month + 1):
        day_key = f"{day:02d}-{month:02d}"
        month_data['days'][day] = days_data.get(day_key, {})

    # Get navigation info
    prev_month, next_month = get_prev_next_month(month)

    navigation = {
        'current_month': month,
        'current_month_name': month_name,
        'prev_month': prev_month,
        'next_month': next_month,
        'prev_month_name': calendar.month_name[prev_month],
        'next_month_name': calendar.month_name[next_month]
    }

    return render_template('index.html',
                         month_data=month_data,
                         current_year=year,
                         navigation=navigation)

@app.route('/verse/<verse_id>')
def verse_detail(verse_id):
    """Detailed view of a specific verse"""
    verse_data = get_verse_details(verse_id)
    if not verse_data:
        return "Verse not found", 404
    
    return render_template('verse_detail.html', verse=verse_data)

@app.route('/edit/<verse_id>')
def edit_verse(verse_id):
    """Edit form for a specific verse"""
    verse_data = get_verse_details(verse_id)
    if not verse_data:
        return "Verse not found", 404
    
    return render_template('edit_verse.html', verse=verse_data)

@app.route('/api/update_verse/<verse_id>', methods=['POST'])
def update_verse(verse_id):
    """API endpoint to update verse data"""
    try:
        data = request.json
        
        # Update main verse document
        verse_ref = db.collection('verses').document(verse_id)
        
        # Update basic verse info
        verse_updates = {}
        if 'imageName' in data:
            verse_updates['imageName'] = data['imageName']
        
        if verse_updates:
            verse_ref.update(verse_updates)
        
        # Update versions
        if 'versions' in data:
            for version_id, version_data in data['versions'].items():
                version_ref = verse_ref.collection('versions').document(version_id)
                version_ref.set({
                    'text': version_data.get('text', ''),
                    'translation': version_data.get('translation', version_id.upper())
                })
        
        return jsonify({'success': True, 'message': 'Verse updated successfully'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/add_version/<verse_id>', methods=['POST'])
def add_version(verse_id):
    """API endpoint to add a new version to a verse"""
    try:
        data = request.json
        version_id = data.get('version_id', '').lower()
        text = data.get('text', '')
        translation = data.get('translation', version_id.upper())

        if not version_id or not text:
            return jsonify({'success': False, 'message': 'Version ID and text are required'}), 400

        verse_ref = db.collection('verses').document(verse_id)
        version_ref = verse_ref.collection('versions').document(version_id)

        version_ref.set({
            'text': text,
            'translation': translation
        })

        return jsonify({'success': True, 'message': 'Version added successfully'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

def create_verse_id_from_reference(book, chapter, verse_start, verse_end=None):
    """Create a verse ID from book, chapter, and verse numbers"""
    if verse_end is None or verse_start == verse_end:
        return f"{book}_{chapter}_{verse_start}"
    else:
        return f"{book}_{chapter}_{verse_start}-{verse_end}"

def parse_verse_reference(reference):
    """Parse a verse reference like 'John 3:16' into components"""
    try:
        # Handle different formats like "John 3:16", "1 Corinthians 13:4", etc.
        parts = reference.strip().split()

        if len(parts) >= 2:
            # Last part should contain chapter:verse
            chapter_verse = parts[-1]
            book_parts = parts[:-1]
            book = ' '.join(book_parts)

            if ':' in chapter_verse:
                chapter, verse_part = chapter_verse.split(':')
                chapter = int(chapter)

                # Handle verse ranges like "16-17"
                if '-' in verse_part:
                    verse_start, verse_end = verse_part.split('-')
                    verse_start = int(verse_start)
                    verse_end = int(verse_end)
                else:
                    verse_start = int(verse_part)
                    verse_end = verse_start

                return book, chapter, verse_start, verse_end
    except (ValueError, IndexError):
        pass

    return None, None, None, None

@app.route('/api/bulk_update', methods=['POST'])
def bulk_update():
    """API endpoint to handle bulk updates from the editable table"""
    try:
        data = request.json
        year = data.get('year', 'default')
        changes = data.get('changes', {})

        if not changes:
            return jsonify({'success': False, 'message': 'No changes provided'}), 400

        batch = db.batch()
        batch_count = 0

        for day_key, change_data in changes.items():
            day = change_data.get('day')
            month = change_data.get('month')
            verse_id = change_data.get('verseId')
            versions = change_data.get('versions', {})
            image_name = change_data.get('imageName')
            reference = change_data.get('reference')

            # Skip if no meaningful changes
            if not versions and not image_name and not reference:
                continue

            # If verse_id is 'new', we need to create a new verse
            if verse_id == 'new' or not verse_id:
                # Try to parse the reference if provided
                if reference:
                    book, chapter, verse_start, verse_end = parse_verse_reference(reference)
                    if book and chapter and verse_start:
                        verse_id = create_verse_id_from_reference(book, chapter, verse_start, verse_end)
                    else:
                        # If parsing fails, create a placeholder
                        verse_id = f"Placeholder_{day:02d}_{month:02d}"
                        book, chapter, verse_start, verse_end = 'Placeholder', month, day, day
                else:
                    # No reference provided, create a placeholder
                    verse_id = f"Placeholder_{day:02d}_{month:02d}"
                    book, chapter, verse_start, verse_end = 'Placeholder', month, day, day

                # Create the verse document
                verse_ref = db.collection('verses').document(verse_id)
                verse_data = {
                    'book': book,
                    'chapter': chapter,
                    'verseStart': verse_start,
                    'verseEnd': verse_end
                }

                if image_name:
                    verse_data['imageName'] = image_name

                batch.set(verse_ref, verse_data)
                batch_count += 1

                # Update the day assignment
                day_ref = db.collection('days').document(day_key)
                day_data = {
                    year: {
                        'verseId': verse_id
                    }
                }
                batch.set(day_ref, day_data, merge=True)
                batch_count += 1

            else:
                # Update existing verse
                verse_ref = db.collection('verses').document(verse_id)

                # Update image name if provided
                if image_name is not None:
                    batch.update(verse_ref, {'imageName': image_name})
                    batch_count += 1

            # Update or create versions
            for version_id, version_data in versions.items():
                text = version_data.get('text', '').strip()
                if text:  # Only save non-empty text
                    verse_ref = db.collection('verses').document(verse_id)
                    version_ref = verse_ref.collection('versions').document(version_id.lower())

                    version_doc = {
                        'text': text,
                        'translation': version_data.get('translation', version_id.upper())
                    }

                    batch.set(version_ref, version_doc)
                    batch_count += 1

            # Commit batch if it's getting large
            if batch_count >= 450:  # Leave some room before Firestore's 500 limit
                batch.commit()
                batch = db.batch()
                batch_count = 0

        # Commit any remaining operations
        if batch_count > 0:
            batch.commit()

        return jsonify({'success': True, 'message': f'Successfully updated {len(changes)} entries'})

    except Exception as e:
        print(f"Error in bulk_update: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
