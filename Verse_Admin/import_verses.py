#!/usr/bin/env python3
import argparse
import csv
from add_verse import add_verse

def import_verses_from_csv(file_path):
    """
    Import verses from a CSV file

    Args:
        file_path (str): Path to the CSV file

    Returns:
        int: Number of verses imported
    """
    count = 0

    try:
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                try:
                    # Extract data from the row
                    book = row['book'].strip()
                    chapter = int(row['chapter'])
                    verse_start = int(row['verse_start'])
                    verse_end = int(row.get('verse_end', verse_start))  # Default to verse_start
                    translation = row['translation'].strip()
                    text = row['text'].strip()
                    image_name = row.get('image_name', '').strip() or None

                    # Add the verse
                    add_verse(book, chapter, verse_start, verse_end, translation, text, image_name)
                    count += 1

                except (KeyError, ValueError) as e:
                    print(f"Error processing row: {row}")
                    print(f"Error details: {e}")

    except Exception as e:
        print(f"Error reading CSV file: {e}")

    print(f"Imported {count} verses")
    return count

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Import verses from a CSV file')
    parser.add_argument('--file', required=True, help='Path to the CSV file')

    args = parser.parse_args()

    # Import the verses
    import_verses_from_csv(args.file)
