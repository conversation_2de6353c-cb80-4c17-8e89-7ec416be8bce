#!/usr/bin/env python3
import argparse
import random
from firebase_config import get_db
from assign_verse import assign_verse

def get_all_verse_ids():
    """
    Get all verse IDs from the database

    Returns:
        list: List of verse IDs
    """
    db = get_db()
    verses_ref = db.collection('verses')
    verses = verses_ref.stream()

    return [verse.id for verse in verses]

def generate_year_assignments(year_label="default"):
    """
    Generate verse assignments for a whole year

    Args:
        year_label (str): Year label to use for assignments (default: "default")

    Returns:
        int: Number of days assigned
    """
    # Get all verse IDs
    verse_ids = get_all_verse_ids()

    if not verse_ids:
        print("Error: No verses found in the database")
        return 0

    # Shuffle the verse IDs to randomize assignments
    random.shuffle(verse_ids)

    # We need 366 days to cover all possible dates (including leap years)
    days_in_year = 366

    # Make sure we have enough verses
    if len(verse_ids) < days_in_year:
        print(f"Warning: Not enough verses ({len(verse_ids)}) for a whole year ({days_in_year} days)")
        print("Some verses will be used multiple times")

        # Repeat the verse IDs to cover the whole year
        while len(verse_ids) < days_in_year:
            verse_ids.extend(verse_ids)

    # Trim to the exact number of days
    verse_ids = verse_ids[:days_in_year]

    # Generate assignments for each day of the year (all possible DD-MM combinations)
    count = 0
    verse_index = 0

    for month in range(1, 13):  # Months 1-12
        # Determine days in this month
        if month in [1, 3, 5, 7, 8, 10, 12]:
            days_in_month = 31
        elif month in [4, 6, 9, 11]:
            days_in_month = 30
        else:  # February
            days_in_month = 29  # Include Feb 29 for leap years

        for day in range(1, days_in_month + 1):
            # Assign a verse to this day
            verse_id = verse_ids[verse_index]
            success = assign_verse(day, month, verse_id, year_label)

            if success:
                count += 1

            verse_index += 1

    print(f"Generated {count} assignments for year label '{year_label}'")
    return count

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate verse assignments for a whole year')
    parser.add_argument('--year', default="default", help='Year label to use for assignments (default: "default")')

    args = parser.parse_args()

    # Generate the assignments
    generate_year_assignments(args.year)
