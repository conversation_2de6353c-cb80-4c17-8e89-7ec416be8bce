#!/usr/bin/env python3
import argparse
from firebase_config import get_db

def add_verse(book, chapter, verse_start, verse_end, translation, text, image_name=None):
    """
    Add a verse to the Firebase database

    Args:
        book (str): Book name (e.g., "<PERSON>")
        chapter (int): Chapter number
        verse_start (int): Starting verse number
        verse_end (int): Ending verse number (same as start for single verse)
        translation (str): Translation abbreviation (e.g., "NIV", "KJV")
        text (str): Verse text
        image_name (str, optional): Image filename

    Returns:
        str: Verse ID
    """
    # Format the verse ID
    if verse_start == verse_end:
        verse_id = f"{book}_{chapter}_{verse_start}"
    else:
        verse_id = f"{book}_{chapter}_{verse_start}-{verse_end}"

    # Get Firestore database
    db = get_db()

    # Create or update the main verse document
    verse_data = {
        "book": book,
        "chapter": chapter,
        "verseStart": verse_start,
        "verseEnd": verse_end
    }

    if image_name:
        verse_data["imageName"] = image_name

    # Add the verse to the verses collection
    verse_ref = db.collection('verses').document(verse_id)
    verse_ref.set(verse_data, merge=True)  # Use merge to preserve existing data

    # Add the translation to the versions subcollection
    translation_data = {
        "text": text,
        "translation": translation.upper()
    }

    version_ref = verse_ref.collection('versions').document(translation.lower())
    version_ref.set(translation_data)

    print(f"Added verse: {verse_id} ({translation})")
    return verse_id

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Add a verse to the Firebase database')
    parser.add_argument('--book', required=True, help='Book name (e.g., "John")')
    parser.add_argument('--chapter', required=True, type=int, help='Chapter number')
    parser.add_argument('--verse_start', required=True, type=int, help='Starting verse number')
    parser.add_argument('--verse_end', type=int, help='Ending verse number (defaults to verse_start)')
    parser.add_argument('--translation', required=True, help='Translation abbreviation (e.g., "NIV", "KJV")')
    parser.add_argument('--text', required=True, help='Verse text')
    parser.add_argument('--image', help='Image filename')

    args = parser.parse_args()

    # Default verse_end to verse_start if not provided
    verse_end = args.verse_end if args.verse_end else args.verse_start

    # Add the verse
    add_verse(args.book, args.chapter, args.verse_start, verse_end, args.translation, args.text, args.image)
