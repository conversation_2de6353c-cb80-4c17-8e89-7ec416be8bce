#!/usr/bin/env python3
import argparse
import requests
import time
import re
from firebase_config import get_db

def parse_verse_id(verse_id):
    """
    Parse a verse ID to extract book, chapter, and verse information

    Args:
        verse_id (str): Verse ID like "John_3_16", "1_Corinthians_13_4", or "Proverbs_3_5-6"

    Returns:
        tuple: (book, chapter, verse_start, verse_end) or None if parsing fails
    """
    try:
        # Handle verse ranges like "Proverbs_3_5-6"
        if '-' in verse_id and not verse_id.startswith('1_') and not verse_id.startswith('2_') and not verse_id.startswith('3_'):
            # Check if the last part contains a range
            parts = verse_id.split('_')
            if '-' in parts[-1]:
                book_parts = parts[:-2]
                chapter = int(parts[-2])
                verse_range = parts[-1].split('-')
                verse_start = int(verse_range[0])
                verse_end = int(verse_range[1])
            else:
                # Single verse
                book_parts = parts[:-2]
                chapter = int(parts[-2])
                verse_start = int(parts[-1])
                verse_end = verse_start
        else:
            # Handle single verses like "John_3_16" or "1_Corinthians_13_4"
            parts = verse_id.split('_')
            book_parts = parts[:-2]
            chapter = int(parts[-2])
            verse_start = int(parts[-1])
            verse_end = verse_start

        # Reconstruct book name and handle numbered books
        book = ' '.join(book_parts)

        # Handle numbered books (1 Corinthians, 2 Peter, etc.)
        if book.startswith('1 '):
            book = '1 ' + book[2:]
        elif book.startswith('2 '):
            book = '2 ' + book[2:]
        elif book.startswith('3 '):
            book = '3 ' + book[2:]

        return book, chapter, verse_start, verse_end
    except (ValueError, IndexError) as e:
        print(f"Error parsing verse ID '{verse_id}': {e}")
        return None

def fetch_verse_from_api(book, chapter, verse_start, verse_end=None):
    """
    Fetch verse text from the Bible API

    Args:
        book (str): Book name
        chapter (int): Chapter number
        verse_start (int): Starting verse number
        verse_end (int, optional): Ending verse number for ranges

    Returns:
        dict: API response or None if failed
    """
    try:
        # Format the API URL
        if verse_end and verse_end != verse_start:
            verse_ref = f"{book} {chapter}:{verse_start}-{verse_end}"
        else:
            verse_ref = f"{book} {chapter}:{verse_start}"

        # The API expects URL-encoded references
        url = f"https://bible-api.com/{verse_ref}?translation=kjv"

        print(f"Fetching: {verse_ref} (KJV)")

        # Make the API request
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        data = response.json()

        # Check if we got valid data
        if 'text' in data and data['text'].strip():
            return data
        else:
            print(f"No text found for {verse_ref}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"API request failed for {verse_ref}: {e}")
        return None
    except Exception as e:
        print(f"Error fetching {verse_ref}: {e}")
        return None

def populate_verses_collection(year="default", delay=5.0):
    """
    Populate the verses collection with KJV text from the Bible API

    Args:
        year (str): Year mapping to read from days collection
        delay (float): Delay between API calls in seconds

    Returns:
        int: Number of verses processed
    """
    # Get Firestore database
    db = get_db()

    # Get all days from the collection
    days_ref = db.collection('days')
    days = days_ref.stream()

    processed_verses = set()  # Track processed verses to avoid duplicates
    count = 0

    for day_doc in days:
        day_data = day_doc.to_dict()

        # Check if the specified year exists in this day
        if year not in day_data:
            continue

        verse_id = day_data[year].get('verseId')
        if not verse_id:
            continue

        # Skip if we've already processed this verse
        if verse_id in processed_verses:
            continue

        processed_verses.add(verse_id)

        # Parse the verse ID
        parsed = parse_verse_id(verse_id)
        if not parsed:
            continue

        book, chapter, verse_start, verse_end = parsed

        # Check if verse already exists in the collection
        verse_ref = db.collection('verses').document(verse_id)
        verse_doc = verse_ref.get()

        if verse_doc.exists:
            # Check if KJV version already exists
            kjv_version_ref = verse_ref.collection('versions').document('kjv')
            kjv_version_doc = kjv_version_ref.get()

            if kjv_version_doc.exists:
                print(f"KJV version already exists for {verse_id}, skipping")
                continue

        # Fetch verse text from API
        api_data = fetch_verse_from_api(book, chapter, verse_start, verse_end)

        if not api_data:
            print(f"Failed to fetch data for {verse_id}")
            continue

        try:
            # Create or update the main verse document
            verse_data = {
                "book": book,
                "chapter": chapter,
                "verseStart": verse_start,
                "verseEnd": verse_end
            }

            # Set the main verse document (merge to preserve existing data)
            verse_ref.set(verse_data, merge=True)

            # Add the KJV translation to the versions subcollection
            kjv_data = {
                "text": api_data['text'].strip(),
                "translation": "KJV"
            }

            kjv_version_ref = verse_ref.collection('versions').document('kjv')
            kjv_version_ref.set(kjv_data)

            print(f"✓ Added KJV text for {verse_id}")
            count += 1

            # Add delay to be respectful to the API
            time.sleep(delay)

        except Exception as e:
            print(f"Error saving {verse_id}: {e}")

    print(f"\nProcessed {count} verses with KJV text")
    return count

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Populate verses collection with KJV text from Bible API')
    parser.add_argument('--year', default="default", help='Year mapping to read from days collection (default: "default")')
    parser.add_argument('--delay', type=float, default=5.0, help='Delay between API calls in seconds (default: 5.0)')

    args = parser.parse_args()

    print(f"Starting to populate verses collection with KJV text...")
    print(f"Reading from year: {args.year}")
    print(f"API delay: {args.delay} seconds (to be respectful to the API)")
    print("-" * 50)

    # Populate the verses collection
    populate_verses_collection(args.year, args.delay)
