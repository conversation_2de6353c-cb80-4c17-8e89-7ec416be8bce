#!/usr/bin/env python3
"""
Simple script to test Firebase connection
"""
from firebase_config import get_db

def test_connection():
    """Test the Firebase connection"""
    try:
        print("Testing Firebase connection...")
        db = get_db()
        
        # Try to read from the database
        print("✓ Successfully connected to Firebase!")
        
        # Check if collections exist
        collections = db.collections()
        collection_names = [col.id for col in collections]
        
        print(f"Available collections: {collection_names}")
        
        # Count documents in each collection
        if 'verses' in collection_names:
            verses_count = len(list(db.collection('verses').stream()))
            print(f"Verses collection: {verses_count} documents")
        
        if 'days' in collection_names:
            days_count = len(list(db.collection('days').stream()))
            print(f"Days collection: {days_count} documents")
        
        return True
        
    except Exception as e:
        print(f"✗ Error connecting to Firebase: {e}")
        return False

if __name__ == "__main__":
    test_connection()
