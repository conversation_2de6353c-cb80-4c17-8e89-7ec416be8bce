from flask import request

def get_client_ip():
    """
    Get the client's real IP address from request headers.
    
    When running behind a proxy or load balancer (like in Google Cloud Run),
    the request.remote_addr will be the IP of the proxy, not the client.
    The X-Forwarded-For header contains the original client IP.
    
    Returns:
        str: The client's IP address
    """
    # Check for X-Forwarded-For header (standard for proxies)
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        # The first IP in the list is the client's IP
        client_ip = forwarded_for.split(',')[0].strip()
        return client_ip
    
    # Fallback to remote_addr if no X-Forwarded-For header
    return request.remote_addr
