from google.cloud import storage
from datetime import datetime, timedelta
import os
import logging
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

GCP_BUCKET_NAME = "verse-view-bucket"

def get_service_account_email():
    try:
        response = requests.get(
            "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/email",
            headers={"Metadata-Flavor": "Google"},
            timeout=2
        )
        response.raise_for_status()
        return response.text
    except Exception as e:
        print(f"Failed to get service account email from metadata server: {e}")
        return None

def get_access_token():
    try:
        response = requests.get(
            "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token",
            headers={"Metadata-Flavor": "Google"},
            timeout=2
        )
        response.raise_for_status()
        return response.json()["access_token"]
    except Exception as e:
        print(f"Failed to get access token: {e}")
        return None

def get_signed_url(blob_name):
    """Generate a signed URL for a blob in the bucket using IAM credentials."""
    try:
        # logger.info(f"Attempting to generate signed URL for blob: {blob_name}")
        # logger.info(f"Using bucket name: {GCP_BUCKET_NAME}")

        if not blob_name:
            logger.warning("No image name provided for signed URL generation")
            return None
        
        #Getting google creds
        service_account_email = get_service_account_email()
        access_token = get_access_token()

        # logger.info(f"Using email: {service_account_email}")
        # logger.info(f"Using token: {access_token}")

        # Initialize storage client with credentials
        client = storage.Client()
        bucket = client.bucket(GCP_BUCKET_NAME)
        blob = bucket.blob(blob_name)

        # Check existence
        if not blob.exists():
            logger.warning(f"Image not found in bucket: {blob_name}")
            return None

        expiration = datetime.utcnow() + timedelta(minutes=15)

        url = blob.generate_signed_url(
            version="v4",
            # This URL is valid for 15 minutes
            expiration=expiration,
            # Allow GET requests using this URL.
            method="GET",
            service_account_email=service_account_email,
            access_token=access_token
        )

        # logger.info(f"Successfully generated signed URL for image: {blob_name}")
        # logger.info(f"Generated URL length: {len(url)} characters")
        return url

    except Exception as e:
        logger.error(f"Error generating signed URL for {blob_name}: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        return None
# Image data mapping dates (in DD-MM format) to image filenames
# Format: "DD-MM": "image_filename.jpg"
IMAGE_DATA = {
    "01-01": "dead-sea1.jpeg",
    "02-01": "negev-winding-road.jpeg",
    "03-01": "masada-sea2.jpg",
    "04-01": "jerrico-wadi.jpeg",
    "05-01": "masada-sea1.jpg",
    "06-01": "camels-in-field.jpg",
    "07-01": "ein-gedi2.jpg",
    "08-01": "mediterranean-ceasera.jpg",
    "09-01": "negev-winding-road.jpeg",
    "10-01": "tomb_inside.jpg",
    "11-01": "camels-in-field.jpg",
    "12-01": "tomb_inside.jpg",
    "13-01": "mediterranean-ceasera.jpg",
    "14-01": "tel-aviv-beach1.jpeg",
    "15-01": "dead-sea-beach.jpg",
    "16-01": "negev-crater1.jpg",
    "17-01": "masada-sea2.jpg",
    "18-01": "tel-aviv-beach1.jpeg",
    "19-01": "ein-gedi3.jpg",
    "20-01": "mountain-tree-lookout3.jpeg",
    "21-01": "rosh-hanikra-cave.jpeg",
    "22-01": "tiberus-sea-of-galilee.jpeg",
    "23-01": "negev-wadi2.jpeg",
    "24-01": "sea-of-galilee1.jpeg",
    "25-01": "mediterranean-ceasera.jpg",
    "26-01": "rosh-hanikra-cave.jpeg",
    "27-01": "black-and-white-arch.jpg",
    "28-01": "tomb_outside.jpg",
    "29-01": "north-valley-hula.jpeg",
    "30-01": "mediterranean-ceasera.jpg",
    "31-01": "ein-gedi1.jpeg",
    "01-02": "negev-winding-road.jpeg",
    "02-02": "tomb_inside.jpg",
    "03-02": "tel-aviv-beach1.jpeg",
    "04-02": "tree-sunset.jpeg",
    "05-02": "sea-of-galilee1.jpeg",
    "06-02": "black-and-white-arch.jpg",
    "07-02": "camels-in-field.jpg",
    "08-02": "north-valley-hula.jpeg",
    "09-02": "jerusalem-hills1.jpeg",
    "10-02": "negev-wadi1.jpeg",
    "11-02": "coconut-tree-row.jpg",
    "12-02": "mediterranean-ceasera.jpg",
    "13-02": "sea-of-galilee1.jpeg",
    "14-02": "negev-crater1.jpg",
    "15-02": "beit-shean1.jpg",
    "16-02": "black-and-white-arch.jpg",
    "17-02": "sea-of-galilee-boat2.jpeg",
    "18-02": "rosh-hanikra-cave.jpeg",
    "19-02": "dead-sea1.jpeg",
    "20-02": "camels-in-field.jpg",
    "21-02": "tel-aviv-beach1.jpeg",
    "22-02": "north-valley-hula.jpeg",
    "23-02": "negev-wadi2.jpeg",
    "24-02": "dead-sea-palm-trees.jpg",
    "25-02": "rosh-hanikra-cave.jpeg",
    "26-02": "black-and-white-arch.jpg",
    "27-02": "ein-gedi2.jpg",
    "28-02": "beit-shean1.jpg",
    "01-03": "black-and-white-arch.jpg",
    "02-03": "sea-of-galilee-boat2.jpeg",
    "03-03": "tel-aviv-beach1.jpeg",
    "04-03": "masada-sea1.jpg",
    "05-03": "tel-aviv-7626789.jpg",
    "06-03": "masada-sunset.jpg",
    "07-03": "camels-in-field.jpg",
    "08-03": "rosh-hanikra-cave.jpeg",
    "09-03": "beit-shean1.jpg",
    "10-03": "tomb_outside.jpg",
    "11-03": "black-and-white-arch.jpg",
    "12-03": "mediterranean-ceasera.jpg",
    "13-03": "tree-sunset.jpeg",
    "14-03": "masada-sunset.jpg",
    "15-03": "masada-sea1.jpg",
    "16-03": "negev-winding-road.jpeg",
    "17-03": "ein-gedi1.jpeg",
    "18-03": "jerrico-wadi.jpeg",
    "19-03": "jaffa-alley.jpg",
    "20-03": "ein-gedi1.jpeg",
    "21-03": "tel-aviv-7626789.jpg",
    "22-03": "keshet-cave.jpg",
    "23-03": "negev-wadi1.jpeg",
    "24-03": "jaffa-beach.jpg",
    "25-03": "tree-sunset.jpeg",
    "26-03": "tiberus-sea-of-galilee.jpeg",
    "27-03": "north-valley-road.jpeg",
    "28-03": "sea-of-galilee1.jpeg",
    "29-03": "ein-gedi2.jpg",
    "30-03": "ein-gedi1.jpeg",
    "31-03": "dead-sea1.jpeg",
    "01-04": "negev-wadi2.jpeg",
    "02-04": "ein-gedi1.jpeg",
    "03-04": "masada-cable-car.jpeg",
    "04-04": "negev-wadi2.jpeg",
    "05-04": "negev-crater2.jpg",
    "06-04": "north-valley-road.jpeg",
    "07-04": "negev-crater2.jpg",
    "08-04": "dead-sea-palm-trees.jpg",
    "09-04": "masada-cable-car.jpeg",
    "10-04": "jaffa-alley.jpg",
    "11-04": "negev-crater1.jpg",
    "12-04": "sea-of-galilee-boat1.jpeg",
    "13-04": "black-and-white-arch.jpg",
    "14-04": "ein-gedi1.jpeg",
    "15-04": "coconut-tree-row.jpg",
    "16-04": "jaffa-alley.jpg",
    "17-04": "mountain-tree-lookout3.jpeg",
    "18-04": "trees-garden.jpg",
    "19-04": "cross-door.jpeg",
    "20-04": "tomb_inside.jpg",
    "21-04": "ein-gedi2.jpg",
    "22-04": "sea-of-galilee-boat1.jpeg",
    "23-04": "negev-wadi2.jpeg",
    "24-04": "ein-gedi3.jpg",
    "25-04": "coconut-tree-row.jpg",
    "26-04": "mountain-tree-lookout1.jpeg",
    "27-04": "north-valley-hula.jpeg",
    "28-04": "dead-sea-beach.jpg",
    "29-04": "negev-crater2.jpg",
    "30-04": "masada-sea1.jpg",
    "01-05": "tiberus-sea-of-galilee.jpeg",
    "02-05": "tomb_outside.jpg",
    "03-05": "beit-shean1.jpg",
    "04-05": "tiberus-sea-of-galilee.jpeg",
    "05-05": "keshet-cave.jpg",
    "06-05": "jaffa-beach.jpg",
    "07-05": "tiberus-sea-of-galilee.jpeg",
    "08-05": "ein-gedi1.jpeg",
    "09-05": "ein-gedi2.jpg",
    "10-05": "jerrico-wadi.jpeg",
    "11-05": "camels-in-field.jpg",
    "12-05": "mountain-tree-lookout2.jpg",
    "13-05": "camels-in-field.jpg",
    "14-05": "keshet-cave.jpg",
    "15-05": "masada-sea1.jpg",
    "16-05": "mountain-tree-lookout3.jpeg",
    "17-05": "negev-winding-road.jpeg",
    "18-05": "tomb_inside.jpg",
    "19-05": "ein-gedi2.jpg",
    "20-05": "masada-mountains.jpg",
    "21-05": "black-and-white-arch.jpg",
    "22-05": "beit-shean1.jpg",
    "23-05": "tel-aviv-7626789.jpg",
    "24-05": "tomb_inside.jpg",
    "25-05": "negev-winding-road.jpeg",
    "26-05": "north-valley-road.jpeg",
    "27-05": "negev-crater2.jpg",
    "28-05": "masada-sea2.jpg",
    "29-05": "keshet-cave.jpg",
    "30-05": "ein-gedi2.jpg",
    "31-05": "sea-of-galilee-boat1.jpeg",
    "01-06": "keshet-cave.jpg",
    "02-06": "ein-gedi3.jpg",
    "03-06": "negev-crater2.jpg",
    "04-06": "ein-gedi2.jpg",
    "05-06": "keshet-cave.jpg",
    "06-06": "mountain-tree-lookout1.jpeg",
    "07-06": "tomb_outside.jpg",
    "08-06": "jerrico-wadi.jpeg",
    "09-06": "masada-cable-car.jpeg",
    "10-06": "dead-sea-palm-trees.jpg",
    "11-06": "tree-sunset.jpeg",
    "12-06": "dead-sea1.jpeg",
    "13-06": "negev-wadi2.jpeg",
    "14-06": "tomb_outside.jpg",
    "15-06": "sea-of-galilee1.jpeg",
    "16-06": "sea-of-galilee-boat2.jpeg",
    "17-06": "sea-of-galilee1.jpeg",
    "18-06": "negev-wadi1.jpeg",
    "19-06": "mountain-tree-lookout2.jpg",
    "20-06": "negev-crater2.jpg",
    "21-06": "tree-sunset.jpeg",
    "22-06": "jerrico-wadi.jpeg",
    "23-06": "jaffa-beach.jpg",
    "24-06": "jerrico-wadi.jpeg",
    "25-06": "black-and-white-arch.jpg",
    "26-06": "tiberus-sea-of-galilee.jpeg",
    "27-06": "keshet-cave.jpg",
    "28-06": "rosh-hanikra-cave.jpeg",
    "29-06": "negv-wadi1.jpeg",
    "30-06": "mediterranean-ceasera.jpg",
    "01-07": "masada-mountains.jpg",
    "02-07": "tel-aviv-beach1.jpeg",
    "03-07": "sea-of-galilee-boat2.jpeg",
    "04-07": "black-and-white-arch.jpg",
    "05-07": "masada-cable-car.jpeg",
    "06-07": "negev-crater1.jpg",
    "07-07": "tel-aviv-7626789.jpg",
    "08-07": "dead-sea-palm-trees.jpg",
    "09-07": "tiberus-sea-of-galilee.jpeg",
    "10-07": "ein-gedi3.jpg",
    "11-07": "negev-winding-road.jpeg",
    "12-07": "mediterranean-ceasera.jpg",
    "13-07": "negev1.jpeg",
    "14-07": "tiberus-sea-of-galilee.jpeg",
    "15-07": "masada-sea1.jpg",
    "16-07": "jerrico-wadi.jpeg",
    "17-07": "negev-wadi1.jpeg",
    "18-07": "tel-aviv-beach1.jpeg",
    "19-07": "negev1.jpeg",
    "20-07": "north-valley-hula.jpeg",
    "21-07": "masada-sunset.jpg",
    "22-07": "keshet-cave.jpg",
    "23-07": "tiberus-sea-of-galilee.jpeg",
    "24-07": "tomb_inside.jpg",
    "25-07": "jerusalem-hills1.jpeg",
    "26-07": "negev-winding-road.jpeg",
    "27-07": "north-valley-hula.jpeg",
    "28-07": "dead-sea1.jpeg",
    "29-07": "masada-cable-car.jpeg",
    "30-07": "jaffa-beach.jpg",
    "31-07": "masada-cable-car.jpeg",
    "01-08": "negev-crater1.jpg",
    "02-08": "tiberus-sea-of-galilee.jpeg",
    "03-08": "ein-gedi2.jpg",
    "04-08": "coconut-tree-row.jpg",
    "05-08": "tomb_outside.jpg",
    "06-08": "tiberus-sea-of-galilee.jpeg",
    "07-08": "masada-sea2.jpg",
    "08-08": "negev-winding-road.jpeg",
    "09-08": "jaffa-beach.jpg",
    "10-08": "dead-sea1.jpeg",
    "11-08": "tel-aviv-beach1.jpeg",
    "12-08": "ein-gedi1.jpeg",
    "13-08": "mountain-tree-lookout2.jpg",
    "14-08": "negv-wadi1.jpeg",
    "15-08": "masada-sea2.jpg",
    "16-08": "keshet-cave.jpg",
    "17-08": "negev1.jpeg",
    "18-08": "tomb_inside.jpg",
    "19-08": "sea-of-galilee-boat1.jpeg",
    "20-08": "rosh-hanikra-cave.jpeg",
    "21-08": "north-valley-hula.jpeg",
    "22-08": "masada-cable-car.jpeg",
    "23-08": "north-valley-hula.jpeg",
    "24-08": "sea-of-galilee1.jpeg",
    "25-08": "ein-gedi2.jpg",
    "26-08": "negev-wadi2.jpeg",
    "27-08": "negv-wadi1.jpeg",
    "28-08": "sea-of-galilee-boat2.jpeg",
    "29-08": "coconut-tree-row.jpg",
    "30-08": "negv-wadi1.jpeg",
    "31-08": "dead-sea-beach.jpg",
    "01-09": "negev1.jpeg",
    "02-09": "tomb_outside.jpg",
    "03-09": "jerusalem-hills1.jpeg",
    "04-09": "negv-wadi1.jpeg",
    "05-09": "beit-shean1.jpg",
    "06-09": "mountain-tree-lookout1.jpeg",
    "07-09": "masada-sea1.jpg",
    "08-09": "masada-sea2.jpg",
    "09-09": "mountain-tree-lookout1.jpeg",
    "10-09": "keshet-cave.jpg",
    "11-09": "mountain-tree-lookout1.jpeg",
    "12-09": "coconut-tree-row.jpg",
    "13-09": "jaffa-alley.jpg",
    "14-09": "masada-sea2.jpg",
    "15-09": "masada-sea1.jpg",
    "16-09": "masada-sunset.jpg",
    "17-09": "negev-wadi2.jpeg",
    "18-09": "negev1.jpeg",
    "19-09": "tiberus-sea-of-galilee.jpeg",
    "20-09": "camels-in-field.jpg",
    "21-09": "mountain-tree-lookout1.jpeg",
    "22-09": "ein-gedi3.jpg",
    "23-09": "tree-sunset.jpeg",
    "24-09": "negev-winding-road.jpeg",
    "25-09": "north-valley-hula.jpeg",
    "26-09": "negev-crater2.jpg",
    "27-09": "masada-sunset.jpg",
    "28-09": "keshet-cave.jpg",
    "29-09": "ein-gedi1.jpeg",
    "30-09": "north-valley-hula.jpeg",
    "01-10": "sea-of-galilee-boat2.jpeg",
    "02-10": "jerusalem-hills1.jpeg",
    "03-10": "north-valley-hula.jpeg",
    "04-10": "tomb_inside.jpg",
    "05-10": "tel-aviv-7626789.jpg",
    "06-10": "sea-of-galilee-boat1.jpeg",
    "07-10": "tiberus-sea-of-galilee.jpeg",
    "08-10": "negev-winding-road.jpeg",
    "09-10": "jerusalem-hills1.jpeg",
    "10-10": "beit-shean1.jpg",
    "11-10": "tel-aviv-7626789.jpg",
    "12-10": "beit-shean1.jpg",
    "13-10": "ein-gedi3.jpg",
    "14-10": "tel-aviv-beach1.jpeg",
    "15-10": "negev-wadi1.jpeg",
    "16-10": "ein-gedi2.jpg",
    "17-10": "camels-in-field.jpg",
    "18-10": "north-valley-road.jpeg",
    "19-10": "mountain-tree-lookout1.jpeg",
    "20-10": "sea-of-galilee-boat2.jpeg",
    "21-10": "mountain-tree-lookout2.jpg",
    "22-10": "beit-shean1.jpg",
    "23-10": "sea-of-galilee-boat2.jpeg",
    "24-10": "tomb_outside.jpg",
    "25-10": "mountain-tree-lookout3.jpeg",
    "26-10": "negv-wadi1.jpeg",
    "27-10": "tomb_inside.jpg",
    "28-10": "sea-of-galilee-boat1.jpeg",
    "29-10": "tomb_inside.jpg",
    "30-10": "ein-gedi2.jpg",
    "31-10": "coconut-tree-row.jpg",
    "01-11": "beit-shean1.jpg",
    "02-11": "jaffa-alley.jpg",
    "03-11": "tomb_inside.jpg",
    "04-11": "mediterranean-ceasera.jpg",
    "05-11": "camels-in-field.jpg",
    "06-11": "black-and-white-arch.jpg",
    "07-11": "tel-aviv-7626789.jpg",
    "08-11": "mediterranean-ceasera.jpg",
    "09-11": "masada-sea1.jpg",
    "10-11": "beit-shean1.jpg",
    "11-11": "dead-sea1.jpeg",
    "12-11": "negev-wadi2.jpeg",
    "13-11": "jerrico-wadi.jpeg",
    "14-11": "masada-sea1.jpg",
    "15-11": "negev-winding-road.jpeg",
    "16-11": "masada-sunset.jpg",
    "17-11": "jaffa-alley.jpg",
    "18-11": "ein-gedi3.jpg",
    "19-11": "tel-aviv-beach1.jpeg",
    "20-11": "keshet-cave.jpg",
    "21-11": "tel-aviv-beach1.jpeg",
    "22-11": "tomb_outside.jpg",
    "23-11": "ein-gedi1.jpeg",
    "24-11": "beit-shean1.jpg",
    "25-11": "negev1.jpeg",
    "26-11": "coconut-tree-row.jpg",
    "27-11": "masada-sunset.jpg",
    "28-11": "negev-crater1.jpg",
    "29-11": "tel-aviv-7626789.jpg",
    "30-11": "sea-of-galilee-boat2.jpeg",
    "01-12": "tel-aviv-7626789.jpg",
    "02-12": "mountain-tree-lookout2.jpg",
    "03-12": "keshet-cave.jpg",
    "04-12": "jaffa-alley.jpg",
    "05-12": "tree-sunset.jpeg",
    "06-12": "jerusalem-hills1.jpeg",
    "07-12": "ein-gedi2.jpg",
    "08-12": "tree-sunset.jpeg",
    "09-12": "tel-aviv-beach1.jpeg",
    "10-12": "rosh-hanikra-cave.jpeg",
    "11-12": "negev-crater2.jpg",
    "12-12": "tomb_inside.jpg",
    "13-12": "jerrico-wadi.jpeg",
    "14-12": "tomb_outside.jpg",
    "15-12": "negv-wadi1.jpeg",
    "16-12": "ein-gedi1.jpeg",
    "17-12": "tomb_inside.jpg",
    "18-12": "jerusalem-hills1.jpeg",
    "19-12": "masada-sea1.jpg",
    "20-12": "sea-of-galilee-boat1.jpeg",
    "21-12": "masada-mountains.jpg",
    "22-12": "masada-cable-car.jpeg",
    "23-12": "tomb_outside.jpg",
    "24-12": "dead-sea1.jpeg",
    "25-12": "sea-of-galilee1.jpeg",
    "26-12": "masada-mountains.jpg",
    "27-12": "negev-crater2.jpg",
    "28-12": "ein-gedi2.jpg",
    "29-12": "ein-gedi1.jpeg",
    "30-12": "mediterranean-ceasera.jpg",
    "31-12": "masada-sunset.jpg"
}