#!/usr/bin/env python3
"""
Test script for the v2 API to verify Firebase integration works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase_config import get_db
from api.v2.routes import get_verse_from_firebase

def test_firebase_connection():
    """Test Firebase connection"""
    try:
        print("Testing Firebase connection...")
        db = get_db()
        
        # Try to read from the database
        print("✓ Successfully connected to Firebase!")
        
        # Check if collections exist
        collections = db.collections()
        collection_names = [col.id for col in collections]
        
        print(f"Available collections: {collection_names}")
        
        # Count documents in each collection
        if 'verses' in collection_names:
            verses_count = len(list(db.collection('verses').stream()))
            print(f"Verses collection: {verses_count} documents")
        
        if 'days' in collection_names:
            days_count = len(list(db.collection('days').stream()))
            print(f"Days collection: {days_count} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Firebase connection failed: {e}")
        return False

def test_get_verse():
    """Test getting a verse from Firebase"""
    try:
        print("\nTesting verse retrieval...")
        
        # Test with a common date
        test_date = "2024-12-25"  # Christmas
        print(f"Testing date: {test_date}")
        
        verse_data = get_verse_from_firebase(test_date, "default")
        
        if verse_data:
            print("✓ Successfully retrieved verse data:")
            print(f"  Reference: {verse_data['reference']}")
            print(f"  Text: {verse_data['text'][:100]}...")
            print(f"  Translation: {verse_data.get('translation', 'N/A')}")
            print(f"  Image: {verse_data.get('image_name', 'N/A')}")
            print(f"  Verse ID: {verse_data.get('verse_id', 'N/A')}")
            return True
        else:
            print(f"❌ No verse found for {test_date}")
            return False
            
    except Exception as e:
        print(f"❌ Error retrieving verse: {e}")
        return False

def test_different_dates():
    """Test with different dates"""
    test_dates = [
        "2024-01-01",  # New Year
        "2024-02-14",  # Valentine's Day
        "2024-07-04",  # Independence Day
        "2024-12-31"   # New Year's Eve
    ]
    
    print("\nTesting multiple dates...")
    success_count = 0
    
    for date in test_dates:
        try:
            verse_data = get_verse_from_firebase(date, "default")
            if verse_data:
                print(f"✓ {date}: {verse_data['reference']}")
                success_count += 1
            else:
                print(f"❌ {date}: No verse found")
        except Exception as e:
            print(f"❌ {date}: Error - {e}")
    
    print(f"\nSuccessfully retrieved {success_count}/{len(test_dates)} verses")
    return success_count > 0

if __name__ == "__main__":
    print("=== V2 API Test ===")
    
    # Test Firebase connection
    if not test_firebase_connection():
        print("❌ Firebase connection failed. Cannot proceed with tests.")
        sys.exit(1)
    
    # Test verse retrieval
    if not test_get_verse():
        print("❌ Verse retrieval failed.")
        sys.exit(1)
    
    # Test multiple dates
    if not test_different_dates():
        print("❌ Multiple date test failed.")
        sys.exit(1)
    
    print("\n✅ All tests passed! V2 API is ready to use.")
