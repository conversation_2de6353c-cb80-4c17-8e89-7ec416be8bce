# Verse_ViewServer

A Flask-based API server for retrieving Bible verses and background images based on dates. The server is deployed to Google Cloud Run.

## Setup

1. Create a virtual environment (recommended):
```bash
python -m venv venv
source venv/bin/activate  # On Windows, use: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Running the Server

```bash
python app.py
```

The server will start on `http://localhost:5000`

## API Endpoint

### GET /getverse

Retrieves a Bible verse for a specific date.

**Query Parameters:**
- `date` (required): Date in YYYY-MM-DD format

**Example Request:**
```
GET http://localhost:5000/getverse?date=2024-01-01
```

**Example Response:**
```json
{
    "reference": "John 1:1",
    "text": "In the beginning was the Word, and the Word was with God, and the Word was God."
}
```

**Error Responses:**
- 400: Invalid date format or missing date parameter
- 404: No verse found for the specified date 


Steps to setup CORs in google storage:
Open google console
https://console.cloud.google.com/

nano cors.json

[
  {
    "origin": [
      "chrome-extension://loecljnfpfcdoafjpfocbadkmehiapmf",
      "chrome-extension://dkjclljkfbajgbnohmcnieihlcmhkmae"
    ],
    "method": ["GET"],
    "responseHeader": ["Content-Type"],
    "maxAgeSeconds": 3600
  }
]

-Press Ctrl + O, Enter to save, then Ctrl + X to exit

gsutil cors set cors.json gs://verse-view-bucket

gsutil cors get gs://verse-view-bucket