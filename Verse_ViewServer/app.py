from flask import Flask
from flask_cors import CORS
from api.v1.routes import v1_bp
from api.v2.routes import v2_bp

app = Flask(__name__)
CORS(app, origins='chrome-extension://dkjclljkfbajgbnohmcnieihlcmhkmae')

# Register blueprints
app.register_blueprint(v1_bp, url_prefix='/api/v1')
app.register_blueprint(v2_bp, url_prefix='/api/v2')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)