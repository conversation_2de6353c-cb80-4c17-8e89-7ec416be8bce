import os
from flask import Blueprint, jsonify, request
from datetime import datetime
from verse_data import VERSE_DATA
from image_data import IMAGE_DATA, get_signed_url
import google.cloud.logging
from google.cloud.logging_v2.resource import Resource
from utils import get_client_ip

# Initialize Google Cloud Logging client
client = google.cloud.logging.Client()
client.setup_logging()
logger = client.logger('verse_view_api')

v1_bp = Blueprint('v1', __name__)

API_KEY = os.environ.get("EXTENSION_API_KEY")
# API_KEY = "s4lNDrBVgjmWhcnsGYDtydWiUx7wphLMCcfeNY_FwAM"

# @v1_bp.route("/test-signed-url")
# def test_signed_url():
#     try:
#         url = get_signed_url("ein-gedi1.jpeg")  # use a known working image
#         return {"url": url}
#     except Exception as e:
#         return {"error": str(e)}, 500

@v1_bp.route('/getverse', methods=['GET'])
def get_verse():
    # Validate API key
    incoming_key = request.headers.get("X-API-Key")
    if incoming_key != API_KEY:
        # Log unauthorized access attempt
        client_ip = get_client_ip()
        logger.log_text(
            f"Unauthorized API access attempt: IP={client_ip}, Key={incoming_key}",
            severity="WARNING"
        )
        return jsonify({"error": "Unauthorized"}), 401

    # Get date from query parameters
    date_str = request.args.get('date')

    if not date_str:
        # Log missing date parameter
        client_ip = get_client_ip()
        logger.log_text(
            f"Missing date parameter: IP={client_ip}",
            severity="WARNING"
        )
        return jsonify({"error": "Date parameter is required"}), 400

    try:
        # Parse the date string
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        # Format as DD-MM for lookup
        lookup_key = date_obj.strftime('%d-%m')

        # Get client IP address
        client_ip = get_client_ip()

        # Log the API call to Google Cloud Logging
        logger.log_text(
            f"API call to getverse: IP={client_ip}, Date={date_str}",
            severity="INFO"
        )

        # Get verse for the date
        verse = VERSE_DATA.get(lookup_key)
        # Get background image for the date
        image_name = IMAGE_DATA.get(lookup_key)

        if verse:
            response = verse.copy()
            if image_name:
                try:
                    # Generate a signed URL for the image using Google Cloud credentials
                    signed_url = get_signed_url(image_name)
                    response['background_image'] = signed_url
                except Exception as e:
                    # If there's an error with Google Cloud, continue without the image
                    error_msg = f"Error generating signed URL: {str(e)}"
                    print(error_msg)
                    # Log the error
                    logger.log_text(
                        f"Error in getverse API: IP={client_ip}, Date={date_str}, Error={error_msg}",
                        severity="ERROR"
                    )
            return jsonify(response)
        else:
            # Log the not found error
            logger.log_text(
                f"No verse found: IP={client_ip}, Date={date_str}, Lookup key={lookup_key}",
                severity="WARNING"
            )
            return jsonify({"error": "No verse found for this date"}), 404

    except ValueError:
        # Log the invalid date format error
        client_ip = get_client_ip()
        logger.log_text(
            f"Invalid date format: IP={client_ip}, Date={date_str}",
            severity="WARNING"
        )
        return jsonify({"error": "Invalid date format. Please use YYYY-MM-DD"}), 400